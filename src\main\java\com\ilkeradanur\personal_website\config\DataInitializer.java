package com.ilkeradanur.personal_website.config;

import com.ilkeradanur.personal_website.entity.User;
import com.ilkeradanur.personal_website.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;

    @Autowired
    public DataInitializer(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Override
    @Transactional
    public void run(String... args) {
        try {
            // Admin kullanıcısı var mı kontrol et
            if (userRepository.findByUsername("<EMAIL>").isEmpty()) {
                // Admin kullanıcısı oluştur
                User adminUser = new User();
                adminUser.setUsername("<EMAIL>");
                adminUser.setPassword("admin"); // Şifreleme olmadığı için düz metin şifre
                adminUser.setRole("ROLE_ADMIN");
                adminUser.setEnabled(true);
                userRepository.save(adminUser);
                System.out.println("Varsayılan admin kullanıcısı oluşturuldu.");
            } else {
                System.out.println("Admin kullanıcısı zaten mevcut.");
            }
        } catch (Exception e) {
            System.err.println("Veritabanı başlatma hatası: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 